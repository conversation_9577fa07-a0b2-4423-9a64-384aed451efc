import React, { useState, useCallback } from 'react';
import documentProcessingService from '../../services/DocumentProcessingService.js';
import DragDropUpload from './DragDropUpload.jsx';
import { useFileUpload } from '../../hooks/useFileUpload.js';
import PipelineVisualization from '../../../components/features/pipeline/PipelineVisualization.jsx';

// Import unified loading components
import { LoadingSpinner } from '../../../components/ui/feedback/LoadingSpinner.jsx';

function UploadPage({ context }) {
  const [error, setError] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [showPipeline, setShowPipeline] = useState(false);

  // Use the custom file upload hook
  const {
    uploadFiles,
    isUploading,
    progress,
    currentFile: hookCurrentFile,
    currentStage,
    errors,
    clearErrors
  } = useFileUpload({
    maxFiles: 10,
    maxSize: 10 * 1024 * 1024, // 10MB
    onError: (errorMessage) => setError(errorMessage),
    onComplete: (result) => {
      console.log('Upload completed:', result);
    }
  });

  // Handle file upload with enhanced pipeline visualization
  const handleFiles = useCallback(async (files) => {
    if (!files || files.length === 0) { return; }

    setError(null);
    clearErrors();

    // Set the current file for pipeline visualization
    const file = files[0]; // Process first file
    setSelectedFile(file);
    setShowPipeline(true);

    // File processor function for the upload hook
    const fileProcessor = async (file, options = {}) => {
      const { onProgress } = options;

      try {
        // Update progress callback
        const progressCallback = (progress) => {
          onProgress?.({
            progress: progress.progress || 0,
            stage: progress.stage || 'processing'
          });
        };

        // Process file with real document processing service
        const result = await documentProcessingService.processDocument(file, progressCallback);

        if (result.success) {
          // Add processed invoice data to context
          context.addInvoice(result.data);
          return result.data;
        }
        throw new Error(result.error);

      } catch (err) {
        console.error('File processing error:', err);
        throw err;
      }
    };

    // Use the upload hook to process files
    try {
      const result = await uploadFiles(files, fileProcessor);
      if (!result.success) {
        setError(result.error || 'Upload failed');
      }
    } catch (err) {
      setError(err.message || 'Upload failed');
    }
  }, [context, uploadFiles, clearErrors]);

  // Handle error from drag drop component
  const handleUploadError = useCallback((errorMessage) => {
    setError(errorMessage);
  }, []);

  // Handle pipeline step completion
  const handleStepComplete = useCallback((result) => {
    console.log('Pipeline step completed:', result);
  }, []);

  // Handle pipeline processing state change
  const handleProcessingChange = useCallback((processing) => {
    // Update processing state if needed
  }, []);

  return (
    <div className="p-6 h-full flex flex-col">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-xl font-semibold text-gray-900">Upload Invoices</h2>
          {selectedFile && (
            <button
              onClick={() => setShowPipeline(!showPipeline)}
              className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
            >
              {showPipeline ? 'Hide Pipeline' : 'Show Pipeline'}
            </button>
          )}
        </div>
        <p className="text-gray-600 text-sm">
          Upload PDF or image files to extract invoice data using AI with enhanced multi-step pipeline
        </p>
      </div>

      {/* Status Cards */}
      <div className="status-card mb-4 grid grid-cols-2 gap-4" data-testid="status-cards">
        <div className="bg-blue-50 p-3 rounded-lg border border-blue-200" data-testid="processed-files-card">
          <div className="text-sm font-medium text-blue-800">Processed Files</div>
          <div className="text-lg font-semibold text-blue-900">{context.invoices?.length || 0}</div>
        </div>
        <div className="bg-green-50 p-3 rounded-lg border border-green-200" data-testid="status-card">
          <div className="text-sm font-medium text-green-800">Status</div>
          <div className="text-lg font-semibold text-green-900">
            {isUploading ? 'Processing...' : 'Ready'}
          </div>
        </div>
      </div>


      {/* System Status Area */}
      <div className="mb-4 p-3 bg-gray-50 rounded-lg border" data-testid="system-status">
        <div className="text-sm font-medium text-gray-700 mb-2">System Status</div>
        <div className="text-xs text-gray-600">
          {isUploading ? (
            <div className="flex items-center space-x-2">
              <LoadingSpinner size="small" color="blue" />
              <span>Processing: {hookCurrentFile} - {currentStage}</span>
            </div>
          ) : (
            <span>Ready for file upload</span>
          )}
        </div>
      </div>

      {/* Enhanced Upload area with new DragDropUpload component */}
      <div className="flex-1 flex flex-col" data-testid="upload-area">
        {!showPipeline ? (
          <DragDropUpload
            onFilesSelected={handleFiles}
            onError={handleUploadError}
            maxFiles={10}
            maxSize={10 * 1024 * 1024} // 10MB
            disabled={isUploading}
            className="flex-1"
            data-testid="drag-drop-upload"
          />
        ) : (
          <div className="flex-1 bg-white rounded-lg border border-gray-200 p-4">
            <PipelineVisualization
              file={selectedFile}
              isProcessing={isUploading}
              onProcessingChange={handleProcessingChange}
              onStepComplete={handleStepComplete}
              onError={handleUploadError}
              autoRun={true}
            />
          </div>
        )}
      </div>

      {/* Error display */}
      {(error || errors.length > 0) && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center">
            <span className="text-red-500 mr-2">⚠️</span>
            <div className="flex-1">
              {error && <p className="text-sm text-red-700">{error}</p>}
              {errors.length > 0 && (
                <div className="text-sm text-red-700">
                  {errors.map((err, index) => (
                    <p key={index}>{err}</p>
                  ))}
                </div>
              )}
            </div>
          </div>
          <button
            onClick={() => {
              setError(null);
              clearErrors();
            }}
            className="mt-2 text-xs text-red-600 hover:text-red-800"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Recent uploads */}
      {context.invoices && context.invoices.length > 0 && (
        <div className="mt-6">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Recent Uploads</h3>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {context.invoices.slice(0, 3).map((invoice) => (
              <div key={invoice.id} className="p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-green-500">✓</span>
                    <span className="text-sm font-medium text-gray-900">{invoice.filename}</span>
                  </div>
                  <span className="text-xs text-gray-500">
                    {new Date(invoice.processedAt).toLocaleTimeString()}
                  </span>
                </div>
                <div className="text-xs text-gray-600 ml-6">
                  <div className="flex justify-between">
                    <span>Invoice: {invoice.number || 'N/A'}</span>
                    <span>Total: {invoice.total_gross ? `${invoice.total_gross} ${invoice.currency || 'PLN'}` : 'N/A'}</span>
                  </div>
                  <div className="mt-1">
                    <span>From: {invoice.seller_name || 'Unknown'}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

    </div>
  );
}

export default UploadPage;
